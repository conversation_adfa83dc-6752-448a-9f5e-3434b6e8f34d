from maix import image, touchscreen

class YellowLineTrackingGUI:
    """黄色线条循迹GUI界面类"""

    def __init__(self, config):
        self.config = config
        self.ts = touchscreen.TouchScreen()
        self.show_binary = False  # 二值化显示模式

        # 黄色LAB阈值 (默认值)
        self.l_min = 50   # L通道最小值
        self.l_max = 100  # L通道最大值
        self.a_min = -20  # A通道最小值
        self.a_max = 20   # A通道最大值
        self.b_min = 20   # B通道最小值
        self.b_max = 127  # B通道最大值

    def draw_gui(self, img):
        """绘制GUI界面"""

        gui_y = self.config['CAMERA_HEIGHT'] - self.config['GUI_HEIGHT']

        # 绘制背景
        img.draw_rect(0, gui_y, self.config['CAMERA_WIDTH'], self.config['GUI_HEIGHT'],
                     image.COLOR_BLACK, thickness=-1)

        # 显示状态信息
        mode = "二值化" if self.show_binary else "原图"
        img.draw_string(5, gui_y + 5, f"显示模式: {mode}", image.COLOR_YELLOW)

        # 显示当前阈值
        threshold_text = f"L:{self.l_min}-{self.l_max} A:{self.a_min}-{self.a_max} B:{self.b_min}-{self.b_max}"
        img.draw_string(5, gui_y + 20, threshold_text, image.COLOR_WHITE)

        # 二值化按钮 (居中显示)
        btn_y = gui_y + 40
        btn_x = (self.config['CAMERA_WIDTH'] - 80) // 2  # 居中计算
        binary_color = image.COLOR_RED if self.show_binary else image.COLOR_BLUE
        img.draw_rect(btn_x, btn_y, 80, self.config['BUTTON_HEIGHT'], binary_color, thickness=2)
        img.draw_string(btn_x + 20, btn_y + 5, "二值化", image.COLOR_WHITE)



    def handle_touch(self):
        """处理触摸事件"""
        try:
            points = self.ts.read()
            if not points:
                return

            # 调试信息
            print(f"Touch data type: {type(points)}")
            print(f"Touch points: {points}")

            x, y = None, None

            # 尝试多种数据格式解析
            try:
                # 方法1: 直接访问前两个元素
                if hasattr(points, '__len__') and len(points) >= 2:
                    if isinstance(points[0], (int, float)) and isinstance(points[1], (int, float)):
                        x, y = int(points[0]), int(points[1])
                        print(f"Method 1 - Direct access: x={x}, y={y}")
                    elif isinstance(points[0], (list, tuple)) and len(points[0]) >= 2:
                        x, y = int(points[0][0]), int(points[0][1])
                        print(f"Method 1 - Nested access: x={x}, y={y}")
            except:
                pass

            # 方法2: 如果方法1失败，尝试其他格式
            if x is None or y is None:
                try:
                    if hasattr(points, 'x') and hasattr(points, 'y'):
                        x, y = int(points.x), int(points.y)
                        print(f"Method 2 - Attribute access: x={x}, y={y}")
                except:
                    pass

            # 方法3: 如果还是失败，尝试字典格式
            if x is None or y is None:
                try:
                    if isinstance(points, dict):
                        x, y = int(points.get('x', 0)), int(points.get('y', 0))
                        print(f"Method 3 - Dict access: x={x}, y={y}")
                except:
                    pass

            if x is None or y is None:
                print("Failed to extract touch coordinates")
                return

            print(f"Final touch coordinates: x={x}, y={y}")

            # 计算按钮区域
            gui_y = self.config['CAMERA_HEIGHT'] - self.config['GUI_HEIGHT']
            btn_y = gui_y + 40
            btn_x = (self.config['CAMERA_WIDTH'] - 80) // 2  # 居中计算

            print(f"GUI area starts at y={gui_y}")
            print(f"Button area: x={btn_x} to {btn_x+80}, y={btn_y} to {btn_y+self.config['BUTTON_HEIGHT']}")

            # 检查是否在按钮区域内
            in_x_range = btn_x <= x <= btn_x + 80
            in_y_range = btn_y <= y <= btn_y + self.config['BUTTON_HEIGHT']

            print(f"Touch in X range: {in_x_range}, Touch in Y range: {in_y_range}")

            # 二值化按钮处理
            if in_y_range and in_x_range:
                print("Button clicked! Toggling binary mode")
                self.show_binary = not self.show_binary
                print(f"Binary mode now: {self.show_binary}")
            else:
                print("Touch outside button area")

        except Exception as e:
            print(f"Touch handling error: {e}")
            import traceback
            traceback.print_exc()

    def get_thresholds(self):
        """获取当前LAB阈值"""
        return [self.l_min, self.l_max, self.a_min, self.a_max, self.b_min, self.b_max]

    def is_binary_mode(self):
        """获取当前是否为二值化显示模式"""
        return self.show_binary