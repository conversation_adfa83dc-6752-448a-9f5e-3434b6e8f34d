from maix import image, touchscreen

class YellowLineTrackingGUI:
    """黄色线条循迹GUI界面类"""

    def __init__(self, config):
        self.config = config
        self.ts = touchscreen.TouchScreen()
        self.show_binary = False  # 二值化显示模式

        # 黄色LAB阈值 (默认值)
        self.l_min = 50   # L通道最小值
        self.l_max = 100  # L通道最大值
        self.a_min = -20  # A通道最小值
        self.a_max = 20   # A通道最大值
        self.b_min = 20   # B通道最小值
        self.b_max = 127  # B通道最大值

    def draw_gui(self, img):
        """绘制GUI界面"""

        gui_y = self.config['CAMERA_HEIGHT'] - self.config['GUI_HEIGHT']

        # 绘制背景
        img.draw_rect(0, gui_y, self.config['CAMERA_WIDTH'], self.config['GUI_HEIGHT'],
                     image.COLOR_BLACK, thickness=-1)

        # 显示状态信息
        mode = "二值化" if self.show_binary else "原图"
        img.draw_string(5, gui_y + 5, f"显示模式: {mode}", image.COLOR_YELLOW)

        # 显示当前阈值
        threshold_text = f"L:{self.l_min}-{self.l_max} A:{self.a_min}-{self.a_max} B:{self.b_min}-{self.b_max}"
        img.draw_string(5, gui_y + 20, threshold_text, image.COLOR_WHITE)

        # 二值化按钮 (居中显示)
        btn_y = gui_y + 40
        btn_x = (self.config['CAMERA_WIDTH'] - 80) // 2  # 居中计算
        binary_color = image.COLOR_RED if self.show_binary else image.COLOR_BLUE
        img.draw_rect(btn_x, btn_y, 80, self.config['BUTTON_HEIGHT'], binary_color, thickness=2)
        img.draw_string(btn_x + 20, btn_y + 5, "二值化", image.COLOR_WHITE)



    def handle_touch(self):
        """处理触摸事件"""
        points = self.ts.read()
        if points:
            try:
                # 处理不同的触摸数据格式
                if isinstance(points[0], (list, tuple)) and len(points[0]) >= 2:
                    x, y = points[0][:2]
                elif isinstance(points[0], int) and len(points) >= 2:
                    x, y = points[0], points[1]
                else:
                    return

                gui_y = self.config['CAMERA_HEIGHT'] - self.config['GUI_HEIGHT']

                # 二值化按钮处理
                btn_y = gui_y + 40
                btn_x = (self.config['CAMERA_WIDTH'] - 80) // 2  # 居中计算

                if btn_y <= y <= btn_y + self.config['BUTTON_HEIGHT']:
                    if btn_x <= x <= btn_x + 80:  # 二值化按钮
                        self.show_binary = not self.show_binary

            except (IndexError, TypeError, ValueError):
                pass  # 忽略触摸数据错误

    def get_thresholds(self):
        """获取当前LAB阈值"""
        return [self.l_min, self.l_max, self.a_min, self.a_max, self.b_min, self.b_max]

    def is_binary_mode(self):
        """获取当前是否为二值化显示模式"""
        return self.show_binary