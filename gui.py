from maix import image, touchscreen

class YellowLineTrackingGUI:
    """黄色线条循迹GUI界面类"""

    def __init__(self, config):
        self.config = config
        self.ts = touchscreen.TouchScreen()
        self.show_binary = False  # 二值化显示模式
        self.manual_mode = False  # 手动取阈值模式
        self.threshold_tolerance = 15  # 阈值容差

        # 黄色LAB阈值 (默认值)
        self.l_min = 50   # L通道最小值
        self.l_max = 100  # L通道最大值
        self.a_min = -20  # A通道最小值
        self.a_max = 20   # A通道最大值
        self.b_min = 20   # B通道最小值
        self.b_max = 127  # B通道最大值

        # 当前图像引用
        self.current_img = None

    def draw_gui(self, img):
        """绘制GUI界面"""
        self.current_img = img  # 保存图像引用用于手动取阈值

        gui_y = self.config['CAMERA_HEIGHT'] - self.config['GUI_HEIGHT']

        # 绘制背景
        img.draw_rect(0, gui_y, self.config['CAMERA_WIDTH'], self.config['GUI_HEIGHT'],
                     image.COLOR_BLACK, thickness=-1)

        # 显示状态信息
        status = "Manual" if self.manual_mode else "Auto"
        mode = "Binary" if self.show_binary else "Normal"
        img.draw_string(5, gui_y + 2, f"{status} | {mode}", image.COLOR_YELLOW)

        # 显示阈值信息
        img.draw_string(5, gui_y + 15, f"L:{self.l_min}-{self.l_max}", image.COLOR_WHITE)
        img.draw_string(120, gui_y + 15, f"A:{self.a_min}-{self.a_max}", image.COLOR_WHITE)
        img.draw_string(220, gui_y + 15, f"B:{self.b_min}-{self.b_max}", image.COLOR_WHITE)

        # 第一行按钮
        btn_y1 = gui_y + 30
        # 手动取阈值按钮
        manual_color = image.COLOR_GREEN if self.manual_mode else image.COLOR_GRAY
        img.draw_rect(5, btn_y1, 60, self.config['BUTTON_HEIGHT'], manual_color, thickness=1)
        img.draw_string(8, btn_y1 + 5, "Manual", image.COLOR_WHITE)

        # 二值化切换按钮
        binary_color = image.COLOR_RED if self.show_binary else image.COLOR_BLUE
        img.draw_rect(70, btn_y1, 50, self.config['BUTTON_HEIGHT'], binary_color, thickness=1)
        img.draw_string(73, btn_y1 + 5, "Binary", image.COLOR_WHITE)

        # 重置按钮
        img.draw_rect(125, btn_y1, 40, self.config['BUTTON_HEIGHT'], image.COLOR_ORANGE, thickness=1)
        img.draw_string(128, btn_y1 + 5, "Reset", image.COLOR_WHITE)

        # 操作提示
        if self.manual_mode:
            img.draw_string(170, btn_y1 + 5, "Click yellow area", image.COLOR_YELLOW)

        # 第二行按钮 - LAB通道微调
        btn_y2 = gui_y + 55
        # L通道按钮
        img.draw_rect(5, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_BLUE, thickness=1)
        img.draw_string(8, btn_y2 + 5, "L-", image.COLOR_WHITE)
        img.draw_rect(45, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_BLUE, thickness=1)
        img.draw_string(48, btn_y2 + 5, "L+", image.COLOR_WHITE)

        # A通道按钮
        img.draw_rect(85, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_GREEN, thickness=1)
        img.draw_string(88, btn_y2 + 5, "A-", image.COLOR_WHITE)
        img.draw_rect(125, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_GREEN, thickness=1)
        img.draw_string(128, btn_y2 + 5, "A+", image.COLOR_WHITE)

        # B通道按钮
        img.draw_rect(165, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_RED, thickness=1)
        img.draw_string(168, btn_y2 + 5, "B-", image.COLOR_WHITE)
        img.draw_rect(205, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_RED, thickness=1)
        img.draw_string(208, btn_y2 + 5, "B+", image.COLOR_WHITE)

        # 范围调整按钮
        img.draw_rect(245, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_PURPLE, thickness=1)
        img.draw_string(248, btn_y2 + 5, "R-", image.COLOR_WHITE)
        img.draw_rect(285, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_PURPLE, thickness=1)
        img.draw_string(288, btn_y2 + 5, "R+", image.COLOR_WHITE)

    def extract_pixel_lab(self, x, y):
        """从指定像素点提取LAB值并设置阈值"""
        if self.current_img is None:
            return

        try:
            # 获取像素RGB值
            pixel = self.current_img.get_pixel(x, y)
            if isinstance(pixel, (list, tuple)) and len(pixel) >= 3:
                r, g, b = pixel[:3]
            else:
                return

            # 简化的RGB到LAB转换
            l_val = int(0.299 * r + 0.587 * g + 0.114 * b)  # 亮度
            a_val = int((r - g) * 0.5)  # 红绿差值
            b_val = int((b - (r + g) * 0.5) * 0.866)  # 蓝黄差值

            # 限制范围
            l_val = max(0, min(100, l_val))
            a_val = max(-128, min(127, a_val))
            b_val = max(-128, min(127, b_val))

            # 根据容差设置阈值范围
            tolerance = self.threshold_tolerance
            self.l_min = max(0, l_val - tolerance)
            self.l_max = min(100, l_val + tolerance)
            self.a_min = max(-128, a_val - tolerance)
            self.a_max = min(127, a_val + tolerance)
            self.b_min = max(-128, b_val - tolerance)
            self.b_max = min(127, b_val + tolerance)

        except Exception:
            pass  # 忽略错误

    def handle_touch(self):
        """处理触摸事件"""
        points = self.ts.read()
        if points:
            try:
                # 处理不同的触摸数据格式
                if isinstance(points[0], (list, tuple)) and len(points[0]) >= 2:
                    x, y = points[0][:2]
                elif isinstance(points[0], int) and len(points) >= 2:
                    x, y = points[0], points[1]
                else:
                    return

                gui_y = self.config['CAMERA_HEIGHT'] - self.config['GUI_HEIGHT']

                # 检查是否点击在图像区域(手动取阈值模式)
                if self.manual_mode and y < gui_y:
                    self.extract_pixel_lab(x, y)
                    return

                btn_y1 = gui_y + 30  # 第一行按钮
                btn_y2 = gui_y + 55  # 第二行按钮

                # 第一行按钮处理
                if btn_y1 <= y <= btn_y1 + self.config['BUTTON_HEIGHT']:
                    if 5 <= x <= 65:  # Manual按钮
                        self.manual_mode = not self.manual_mode
                    elif 70 <= x <= 120:  # Binary按钮
                        self.show_binary = not self.show_binary
                    elif 125 <= x <= 165:  # Reset按钮
                        self.reset_thresholds()

                # 第二行按钮处理
                elif btn_y2 <= y <= btn_y2 + self.config['BUTTON_HEIGHT']:
                    if 5 <= x <= 40:  # L-
                        self.l_min = max(0, self.l_min - 5)
                        self.l_max = max(self.l_min + 5, self.l_max)
                    elif 45 <= x <= 80:  # L+
                        self.l_max = min(100, self.l_max + 5)
                        self.l_min = min(self.l_max - 5, self.l_min)
                    elif 85 <= x <= 120:  # A-
                        self.a_min = max(-128, self.a_min - 5)
                        self.a_max = max(self.a_min + 5, self.a_max)
                    elif 125 <= x <= 160:  # A+
                        self.a_max = min(127, self.a_max + 5)
                        self.a_min = min(self.a_max - 5, self.a_min)
                    elif 165 <= x <= 200:  # B-
                        self.b_min = max(-128, self.b_min - 5)
                        self.b_max = max(self.b_min + 5, self.b_max)
                    elif 205 <= x <= 240:  # B+
                        self.b_max = min(127, self.b_max + 5)
                        self.b_min = min(self.b_max - 5, self.b_min)
                    elif 245 <= x <= 280:  # R- (缩小范围)
                        self.l_min = min(self.l_max - 5, self.l_min + 2)
                        self.l_max = max(self.l_min + 5, self.l_max - 2)
                        self.a_min = min(self.a_max - 5, self.a_min + 2)
                        self.a_max = max(self.a_min + 5, self.a_max - 2)
                        self.b_min = min(self.b_max - 5, self.b_min + 2)
                        self.b_max = max(self.b_min + 5, self.b_max - 2)
                    elif 285 <= x <= 320:  # R+ (扩大范围)
                        self.l_min = max(0, self.l_min - 2)
                        self.l_max = min(100, self.l_max + 2)
                        self.a_min = max(-128, self.a_min - 2)
                        self.a_max = min(127, self.a_max + 2)
                        self.b_min = max(-128, self.b_min - 2)
                        self.b_max = min(127, self.b_max + 2)

            except (IndexError, TypeError, ValueError):
                pass  # 忽略触摸数据错误

    def reset_thresholds(self):
        """重置阈值到默认值"""
        self.l_min = 50
        self.l_max = 100
        self.a_min = -20
        self.a_max = 20
        self.b_min = 20
        self.b_max = 127

    def get_thresholds(self):
        """获取当前LAB阈值"""
        return [self.l_min, self.l_max, self.a_min, self.a_max, self.b_min, self.b_max]

    def is_binary_mode(self):
        """获取当前是否为二值化显示模式"""
        return self.show_binary

    def is_manual_mode(self):
        """获取当前是否为手动取阈值模式"""
        return self.manual_mode

    def set_threshold_tolerance(self, tolerance):
        """设置阈值容差"""
        self.threshold_tolerance = max(5, min(50, tolerance))