from maix import camera, display, image

# 配置参数
CONFIG = {
    'camera_width': 320,
    'camera_height': 240,
    'yellow_threshold': [[50, 100, -20, 20, 20, 127]],  # 黄色LAB阈值
    'area_threshold': 100,
    'line_color': image.COLOR_RED,
    'text_color': image.COLOR_BLUE
}

cam = camera.Camera(CONFIG['camera_width'], CONFIG['camera_height'])
disp = display.Display()

while True:
    img = cam.read()

    # 黄色线条检测
    lines = img.get_regression(CONFIG['yellow_threshold'], area_threshold=CONFIG['area_threshold'])

    for line in lines:
        # 绘制检测到的线条
        img.draw_line(line.x1(), line.y1(), line.x2(), line.y2(), CONFIG['line_color'], 2)

        # 计算角度和距离
        theta = line.theta()
        rho = line.rho()

        # 角度标准化到0-180度
        if theta > 90:
            theta = 270 - theta
        else:
            theta = 90 - theta

        # 显示循迹参数
        info_text = f"theta: {theta:.1f}, rho: {rho:.1f}"
        img.draw_string(0, 0, info_text, CONFIG['text_color'])

        # 绘制图像中心点
        center_x = CONFIG['camera_width'] // 2
        center_y = CONFIG['camera_height'] // 2
        img.draw_circle(center_x, center_y, 3, image.COLOR_GREEN, 2)

    disp.show(img)