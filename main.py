from maix import camera, display, image
from gui import YellowLineTrackingGUI

# 配置参数
CONFIG = {
    'CAMERA_WIDTH': 320,
    'CAMERA_HEIGHT': 240,
    'GUI_HEIGHT': 70,  # 简化界面，减少高度
    'BUTTON_HEIGHT': 25,  # 增加按钮高度便于点击
    'area_threshold': 10,
    'line_color': image.COLOR_RED,
    'text_color': image.COLOR_BLUE
}

# 初始化设备和GUI
cam = camera.Camera(CONFIG['CAMERA_WIDTH'], CONFIG['CAMERA_HEIGHT'])
disp = display.Display()
gui = YellowLineTrackingGUI(CONFIG)

while True:
    img = cam.read()

    # 处理触摸事件
    gui.handle_touch()

    # 获取当前阈值
    yellow_threshold = [gui.get_thresholds()]

    # 根据显示模式处理图像
    if gui.is_binary_mode():
        # 显示二值化图像
        display_img = img.binary(yellow_threshold)
    else:
        # 显示原始图像
        display_img = img.copy()

    # 黄色线条检测
    lines = img.get_regression(yellow_threshold, area_threshold=CONFIG['area_threshold'])

    for line in lines:
        # 绘制检测到的线条
        display_img.draw_line(line.x1(), line.y1(), line.x2(), line.y2(), CONFIG['line_color'], 2)

        # 计算角度和距离
        theta = line.theta()
        rho = line.rho()

        # 角度标准化到0-180度
        if theta > 90:
            theta = 270 - theta
        else:
            theta = 90 - theta

        # 显示循迹参数
        info_text = f"theta: {theta:.1f}, rho: {rho:.1f}"
        display_img.draw_string(220, 100, info_text, CONFIG['text_color'])

        # 绘制图像中心点
        center_x = CONFIG['CAMERA_WIDTH'] // 2
        center_y = CONFIG['CAMERA_HEIGHT'] // 2
        display_img.draw_circle(center_x, center_y, 3, image.COLOR_GREEN, 2)

    # 绘制GUI界面
    gui.draw_gui(display_img)

    # 显示图像
    disp.show(display_img)