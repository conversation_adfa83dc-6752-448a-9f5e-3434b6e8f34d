from maix import camera, display, image
import time
from gui import YellowLineTrackingGUI

# 配置参数
CONFIG = {
    'CAMERA_WIDTH': 320,
    'CAMERA_HEIGHT': 240,
    'AREA_THRESHOLD': 100,     # 面积阈值
    'LINE_WIDTH': 2,           # 线条宽度
    'GUI_HEIGHT': 80,          # GUI区域高度
    'BUTTON_HEIGHT': 20,       # 按钮高度
    'BUTTON_WIDTH': 50,        # 按钮宽度
}

# 初始化硬件和GUI (不使用串口)
cam = camera.Camera(CONFIG['CAMERA_WIDTH'], CONFIG['CAMERA_HEIGHT'])
disp = display.Display()
gui = YellowLineTrackingGUI(CONFIG)


def process_yellow_line_tracking(img, thresholds):
    """处理黄色线条循迹逻辑"""
    # 黄色线路阈值 (LAB色彩空间)
    yellow_thresholds = [thresholds]

    # 获取线性回归结果
    lines = img.get_regression(yellow_thresholds, area_threshold=CONFIG['AREA_THRESHOLD'])

    for line in lines:
        # 绘制检测到的线条
        img.draw_line(line.x1(), line.y1(), line.x2(), line.y2(),
                     image.COLOR_GREEN, CONFIG['LINE_WIDTH'])

        # 计算角度和距离
        theta = line.theta()
        rho = line.rho()

        # 角度标准化
        if theta > 90:
            theta = 270 - theta
        else:
            theta = 90 - theta

        # 显示参数
        img.draw_string(5, 5, f"Angle:{theta:.1f} Rho:{rho:.1f}", image.COLOR_BLUE)

        # 绘制中心点
        center_x = CONFIG['CAMERA_WIDTH'] // 2
        center_y = CONFIG['CAMERA_HEIGHT'] // 2
        img.draw_circle(center_x, center_y, 3, image.COLOR_RED, -1)

    return len(lines) > 0


def create_binary_image(img, thresholds):
    """创建二值化图像用于显示"""
    binary_img = img.copy()
    yellow_thresholds = [thresholds]
    binary_img = binary_img.binary(yellow_thresholds)
    return binary_img

        # 第一行按钮 - 手动取阈值和二值化切换
        btn_y1 = gui_y + 30
        # 手动取阈值按钮
        manual_color = image.COLOR_GREEN if self.manual_threshold_mode else image.COLOR_GRAY
        img.draw_rect(5, btn_y1, 60, self.config['BUTTON_HEIGHT'], manual_color, thickness=1)
        img.draw_string(8, btn_y1 + 5, "Manual", image.COLOR_WHITE)

        # 二值化切换按钮
        binary_color = image.COLOR_RED if self.show_binary else image.COLOR_BLUE
        img.draw_rect(70, btn_y1, 50, self.config['BUTTON_HEIGHT'], binary_color, thickness=1)
        img.draw_string(73, btn_y1 + 5, "Binary", image.COLOR_WHITE)

        # 重置按钮
        img.draw_rect(125, btn_y1, 40, self.config['BUTTON_HEIGHT'], image.COLOR_ORANGE, thickness=1)
        img.draw_string(128, btn_y1 + 5, "Reset", image.COLOR_WHITE)

        # 提示信息
        if self.manual_threshold_mode:
            img.draw_string(170, btn_y1 + 5, "Click yellow area", image.COLOR_YELLOW)

        # 第二行按钮 - LAB通道调整
        btn_y2 = gui_y + 55
        # L通道按钮
        img.draw_rect(5, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_BLUE, thickness=1)
        img.draw_string(8, btn_y2 + 5, "L-", image.COLOR_WHITE)
        img.draw_rect(45, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_BLUE, thickness=1)
        img.draw_string(48, btn_y2 + 5, "L+", image.COLOR_WHITE)

        # A通道按钮
        img.draw_rect(85, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_GREEN, thickness=1)
        img.draw_string(88, btn_y2 + 5, "A-", image.COLOR_WHITE)
        img.draw_rect(125, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_GREEN, thickness=1)
        img.draw_string(128, btn_y2 + 5, "A+", image.COLOR_WHITE)

        # B通道按钮
        img.draw_rect(165, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_RED, thickness=1)
        img.draw_string(168, btn_y2 + 5, "B-", image.COLOR_WHITE)
        img.draw_rect(205, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_RED, thickness=1)
        img.draw_string(208, btn_y2 + 5, "B+", image.COLOR_WHITE)

        # 范围调整按钮
        img.draw_rect(245, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_PURPLE, thickness=1)
        img.draw_string(248, btn_y2 + 5, "R-", image.COLOR_WHITE)
        img.draw_rect(285, btn_y2, 35, self.config['BUTTON_HEIGHT'], image.COLOR_PURPLE, thickness=1)
        img.draw_string(288, btn_y2 + 5, "R+", image.COLOR_WHITE)

    def extract_pixel_lab(self, x, y):
        """提取指定像素点的LAB值并设置阈值"""
        if self.current_img is None:
            return

        try:
            # 获取像素RGB值
            pixel = self.current_img.get_pixel(x, y)
            if isinstance(pixel, (list, tuple)) and len(pixel) >= 3:
                r, g, b = pixel[:3]
            else:
                return

            # 简化的RGB到LAB转换 (近似算法)
            # 这里使用简化的转换，实际应用中可能需要更精确的转换
            l_val = int(0.299 * r + 0.587 * g + 0.114 * b)  # 亮度近似
            a_val = int((r - g) * 0.5)  # 红绿差值
            b_val = int((b - (r + g) * 0.5) * 0.866)  # 蓝黄差值

            # 限制范围
            l_val = max(0, min(100, l_val))
            a_val = max(-128, min(127, a_val))
            b_val = max(-128, min(127, b_val))

            # 根据容差设置阈值范围
            tolerance = self.threshold_tolerance
            self.l_min = max(0, l_val - tolerance)
            self.l_max = min(100, l_val + tolerance)
            self.a_min = max(-128, a_val - tolerance)
            self.a_max = min(127, a_val + tolerance)
            self.b_min = max(-128, b_val - tolerance)
            self.b_max = min(127, b_val + tolerance)

        except Exception:
            # 如果提取失败，忽略
            pass

    def handle_touch(self):
        """处理触摸事件"""
        points = self.ts.read()
        if points:
            try:
                # 处理不同的触摸数据格式
                if isinstance(points[0], (list, tuple)) and len(points[0]) >= 2:
                    x, y = points[0][:2]  # 如果是列表或元组格式
                elif isinstance(points[0], int) and len(points) >= 2:
                    x, y = points[0], points[1]  # 如果是直接的坐标值
                else:
                    return  # 无法解析触摸数据，直接返回

                gui_y = self.config['CAMERA_HEIGHT'] - self.config['GUI_HEIGHT']
                btn_y1 = gui_y + 30  # 第一行按钮
                btn_y2 = gui_y + 55  # 第二行按钮

                # 检查是否点击在图像区域(手动取阈值模式)
                if self.manual_threshold_mode and y < gui_y:
                    self.extract_pixel_lab(x, y)
                    return

                # 第一行按钮处理
                if btn_y1 <= y <= btn_y1 + self.config['BUTTON_HEIGHT']:
                    if 5 <= x <= 65:  # Manual 手动取阈值模式切换
                        self.manual_threshold_mode = not self.manual_threshold_mode
                    elif 70 <= x <= 120:  # Binary 二值化模式切换
                        self.show_binary = not self.show_binary
                    elif 125 <= x <= 165:  # Reset 重置阈值
                        self.reset_thresholds()

                # 第二行按钮处理
                elif btn_y2 <= y <= btn_y2 + self.config['BUTTON_HEIGHT']:
                    if 5 <= x <= 40:  # L-
                        self.l_min = max(0, self.l_min - 5)
                        self.l_max = max(self.l_min + 5, self.l_max)
                    elif 45 <= x <= 80:  # L+
                        self.l_max = min(100, self.l_max + 5)
                        self.l_min = min(self.l_max - 5, self.l_min)
                    elif 85 <= x <= 120:  # A-
                        self.a_min = max(-128, self.a_min - 5)
                        self.a_max = max(self.a_min + 5, self.a_max)
                    elif 125 <= x <= 160:  # A+
                        self.a_max = min(127, self.a_max + 5)
                        self.a_min = min(self.a_max - 5, self.a_min)
                    elif 165 <= x <= 200:  # B-
                        self.b_min = max(-128, self.b_min - 5)
                        self.b_max = max(self.b_min + 5, self.b_max)
                    elif 205 <= x <= 240:  # B+
                        self.b_max = min(127, self.b_max + 5)
                        self.b_min = min(self.b_max - 5, self.b_min)
                    elif 245 <= x <= 280:  # R- (缩小范围)
                        self.l_min = min(self.l_max - 5, self.l_min + 2)
                        self.l_max = max(self.l_min + 5, self.l_max - 2)
                        self.a_min = min(self.a_max - 5, self.a_min + 2)
                        self.a_max = max(self.a_min + 5, self.a_max - 2)
                        self.b_min = min(self.b_max - 5, self.b_min + 2)
                        self.b_max = max(self.b_min + 5, self.b_max - 2)
                    elif 285 <= x <= 320:  # R+ (扩大范围)
                        self.l_min = max(0, self.l_min - 2)
                        self.l_max = min(100, self.l_max + 2)
                        self.a_min = max(-128, self.a_min - 2)
                        self.a_max = min(127, self.a_max + 2)
                        self.b_min = max(-128, self.b_min - 2)
                        self.b_max = min(127, self.b_max + 2)
            except (IndexError, TypeError, ValueError):
                # 如果触摸数据格式不正确，忽略此次触摸
                pass

    def reset_thresholds(self):
        """重置阈值到默认值"""
        self.l_min = self.config['YELLOW_THRESHOLD_L_MIN']
        self.l_max = self.config['YELLOW_THRESHOLD_L_MAX']
        self.a_min = self.config['YELLOW_THRESHOLD_A_MIN']
        self.a_max = self.config['YELLOW_THRESHOLD_A_MAX']
        self.b_min = self.config['YELLOW_THRESHOLD_B_MIN']
        self.b_max = self.config['YELLOW_THRESHOLD_B_MAX']

    def get_thresholds(self):
        """获取当前阈值设置"""
        return [self.l_min, self.l_max, self.a_min, self.a_max, self.b_min, self.b_max]

    def is_binary_mode(self):
        """获取当前显示模式"""
        return self.show_binary

    def is_manual_mode(self):
        """获取当前是否为手动取阈值模式"""
        return self.manual_threshold_mode

    def set_threshold_tolerance(self, tolerance):
        """设置阈值容差"""
        self.threshold_tolerance = max(5, min(50, tolerance))
